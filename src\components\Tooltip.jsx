import TooltipProfessional from "./TooltipProfessional";

/**
 * Backward-compatible Tooltip component
 *
 * This component maintains the existing API while using the new professional tooltip system.
 * It serves as a migration bridge to ensure existing components continue to work seamlessly.
 */
const Tooltip = ({
  children,
  content,
  position = "top",
  maxWidth = "500px",
  delay = 0,
  disabled = false,
  className = "",
  // Additional props for enhanced functionality
  variant = "standard",
  showArrow = true,
  hideDelay = 300,
  ...props
}) => {
  // Convert maxWidth from string to number if needed
  const maxWidthValue =
    typeof maxWidth === "string"
      ? parseInt(maxWidth.replace("px", ""))
      : maxWidth;

  // Don't render tooltip if disabled or no content
  if (disabled || !content) {
    return children;
  }

  return (
    <TooltipProfessional
      content={content}
      position={position}
      delay={delay}
      hideDelay={hideDelay}
      maxWidth={maxWidthValue}
      className={className}
      disabled={disabled}
      variant={variant}
      showArrow={showArrow}
      {...props}
    >
      {children}
    </TooltipProfessional>
  );
};

export default Tooltip;
